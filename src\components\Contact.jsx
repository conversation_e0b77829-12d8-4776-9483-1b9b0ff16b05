import { Phone, Mail, MapPin, Clock, Send } from 'lucide-react'
import { Button } from '@/components/ui/button'

const Contact = () => {
  const contactInfo = [
    {
      icon: Phone,
      title: 'Phone Numbers',
      details: [
        { label: 'CEO - <PERSON><PERSON>', value: '+966559789339' },
        { label: '<PERSON> - <PERSON>', value: '+966571384069' },
        { label: 'General Manager - <PERSON><PERSON><PERSON>', value: '+966502848208' },
        { label: 'Technical Manager - <PERSON>', value: '+966539084883' }
      ]
    },
    {
      icon: Mail,
      title: 'Email',
      details: [
        { label: 'General Inquiries', value: '<EMAIL>' },
        { label: 'Projects', value: '<EMAIL>' },
        { label: 'Careers', value: '<EMAIL>' }
      ]
    },
    {
      icon: MapPin,
      title: 'Office Locations',
      details: [
        { label: 'Head Office', value: 'Ju<PERSON>il, Saudi Arabia' },
        { label: 'Branch Office', value: '<PERSON><PERSON>, Saudi Arabia' },
        { label: 'Branch Office', value: 'Bahrain' }
      ]
    }
  ]

  const teamMembers = [
    {
      name: '<PERSON><PERSON>',
      position: 'CEO',
      phone: '+96655978933<PERSON>',
      nameArabic: 'نهار راشد بوعيدين'
    },
    {
      name: 'Abdul Jaleel',
      position: 'Administration',
      phone: '+966571384069',
      nameArabic: 'عبد الحليل'
    },
    {
      name: 'G.M.Jilani',
      position: 'General Manager',
      phone: '+966502848208',
      nameArabic: 'جي إم جياشي'
    },
    {
      name: 'Ahmed Shaik.M',
      position: 'Technical Manager',
      phone: '+966539084883',
      nameArabic: 'أحمد شيخ م'
    },
    {
      name: 'Saeed Sira',
      position: 'Marketing & Safety Manager',
      nameArabic: 'سعید میرا'
    }
  ]

  return (
    <section id="contact" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
            Contact Us
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Get in Touch
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Ready to start your next project? Contact our expert team for consultation, 
            quotes, and detailed project proposals.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <div className="bg-gray-50 rounded-2xl p-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
            <form className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Your first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Your last name"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="+966 XXX XXX XXX"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Project Type
                </label>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                  <option>Select project type</option>
                  <option>Oil & Gas Construction</option>
                  <option>Refineries & Petrochemicals</option>
                  <option>Power & Desalination</option>
                  <option>Specialized Services</option>
                  <option>Other</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message
                </label>
                <textarea
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Tell us about your project requirements..."
                ></textarea>
              </div>
              
              <Button className="w-full bg-primary hover:bg-primary/90 text-white">
                Send Message
                <Send className="ml-2 h-4 w-4" />
              </Button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Team Members */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Our Team</h3>
              <div className="space-y-4">
                {teamMembers.map((member, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{member.name}</h4>
                        <p className="text-sm text-gray-600">{member.nameArabic}</p>
                        <p className="text-sm text-primary font-medium">{member.position}</p>
                      </div>
                      {member.phone && (
                        <a
                          href={`tel:${member.phone}`}
                          className="flex items-center text-primary hover:text-primary/80 transition-colors"
                        >
                          <Phone className="w-4 h-4 mr-2" />
                          <span className="text-sm font-medium">{member.phone}</span>
                        </a>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Contact Methods */}
            <div className="space-y-6">
              {contactInfo.map((info, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                      <info.icon className="w-5 h-5 text-primary" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">{info.title}</h4>
                  </div>
                  <div className="space-y-2">
                    {info.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">{detail.label}:</span>
                        <span className="text-sm font-medium text-gray-900">{detail.value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Business Hours */}
            <div className="bg-primary/5 rounded-lg p-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mr-3">
                  <Clock className="w-5 h-5 text-primary" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900">Business Hours</h4>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Sunday - Thursday:</span>
                  <span className="font-medium">8:00 AM - 5:00 PM</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Friday - Saturday:</span>
                  <span className="font-medium">Closed</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">Ready to Start Your Project?</h3>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Contact us today for a free consultation and let our expert team help bring your vision to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-primary hover:bg-gray-100">
              Call Now: +966559789339
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              Email Us
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Contact

