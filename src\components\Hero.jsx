import { ArrowR<PERSON>, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import ThreeDImageCarousel from './ThreeDImageCarousel'

const Hero = () => {
  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      style={{
        background: `
          linear-gradient(135deg, #667eea 0%, #764ba2 100%),
          radial-gradient(circle at 20% 80%, #120078 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, #ff6b6b 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, #4ecdc4 0%, transparent 50%)
        `,
        backgroundBlendMode: 'multiply, normal, normal, normal'
      }}
    >
      {/* Enhanced Background Effects */}
      <div className="absolute inset-0">
        {/* Animated gradient overlay */}
        <div
          className="absolute inset-0 opacity-30"
          style={{
            background: `
              linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%),
              linear-gradient(-45deg, transparent 30%, rgba(255,255,255,0.05) 50%, transparent 70%)
            `,
            animation: 'shimmer 8s ease-in-out infinite'
          }}
        />

        {/* Floating geometric shapes */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute opacity-10"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                width: `${20 + Math.random() * 40}px`,
                height: `${20 + Math.random() * 40}px`,
                background: 'linear-gradient(45deg, #60a5fa, #3b82f6)',
                borderRadius: Math.random() > 0.5 ? '50%' : '10%',
                animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`,
                transform: `rotate(${Math.random() * 360}deg)`
              }}
            />
          ))}
        </div>

        {/* Grid pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }} />
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div
            className="text-center lg:text-left"
            style={{
              transform: 'perspective(1000px) rotateY(-5deg)',
              transformStyle: 'preserve-3d'
            }}
          >
            <div className="inline-flex items-center px-4 py-2 bg-white/20 backdrop-blur-md rounded-full text-sm font-medium text-white mb-6 border border-white/30 shadow-lg">
              <CheckCircle className="w-4 h-4 mr-2 text-green-400" />
              ISO 9001 Certified Company
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight mb-6 drop-shadow-2xl">
              Premier Construction &{' '}
              <span
                className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent"
                style={{
                  textShadow: '0 0 30px rgba(255,255,255,0.5)'
                }}
              >
                Engineering
              </span>{' '}
              Services
            </h1>

            <p className="text-xl text-white/90 mb-8 max-w-2xl drop-shadow-lg">
              Elite Structure Company has been delivering excellence in the Saudi market since 2008.
              We are a premier sub-contractor for SAUDI ARAMCO, SABIC, and other prestigious companies.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mb-8">
              <Button
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-2xl transform hover:scale-105 transition-all duration-300 border-0"
                style={{
                  boxShadow: '0 10px 30px rgba(59, 130, 246, 0.4)'
                }}
              >
                Our Services
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white/30 text-white hover:bg-white/10 backdrop-blur-md shadow-lg transform hover:scale-105 transition-all duration-300"
              >
                View Projects
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-white/20">
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-yellow-400 drop-shadow-lg">15+</div>
                <div className="text-sm text-white/80">Years Experience</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-yellow-400 drop-shadow-lg">500+</div>
                <div className="text-sm text-white/80">Projects Completed</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-3xl font-bold text-yellow-400 drop-shadow-lg">100%</div>
                <div className="text-sm text-white/80">Client Satisfaction</div>
              </div>
            </div>
          </div>

          {/* 3D Image Carousel */}
          <div
            className="relative"
            style={{
              transform: 'perspective(1000px) rotateY(5deg) rotateX(-2deg)',
              transformStyle: 'preserve-3d'
            }}
          >
            <div
              className="aspect-square rounded-2xl overflow-hidden"
              style={{
                filter: 'drop-shadow(0 25px 50px rgba(0,0,0,0.4))',
                transform: 'translateZ(50px)'
              }}
            >
              <ThreeDImageCarousel />
            </div>

            {/* Floating elements around carousel */}
            <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full opacity-80 animate-pulse" />
            <div className="absolute -bottom-6 -left-6 w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-bounce" />
            <div className="absolute top-1/2 -right-8 w-6 h-6 bg-gradient-to-r from-green-400 to-blue-500 rounded-full opacity-70" style={{ animation: 'float 3s ease-in-out infinite' }} />
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  )
}

export default Hero

