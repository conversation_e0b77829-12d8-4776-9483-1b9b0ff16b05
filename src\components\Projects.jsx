import { Calendar, MapPin, CheckCircle, Clock } from 'lucide-react'

const Projects = () => {
  const completedProjects = [
    {
      title: 'NATCO Foundation Project',
      location: 'Saudi Arabia',
      status: 'Completed',
      description: 'Major foundation construction project for NATCO facilities',
      category: 'Civil Construction'
    },
    {
      title: 'JWAP Access Gate Project',
      location: 'Jubail',
      status: 'Completed',
      description: 'Access gate construction and security infrastructure',
      category: 'Infrastructure'
    },
    {
      title: 'GRP Work at Fadhili Gas',
      location: 'Fadhili',
      status: 'Completed',
      description: 'Glass Reinforced Plastic piping installation and maintenance',
      category: 'Piping Systems'
    },
    {
      title: 'SEC Scaffolding Projects',
      location: 'Eastern Province',
      status: 'Completed',
      description: 'Comprehensive scaffolding services for Saudi Electricity Company',
      category: 'Scaffolding'
    }
  ]

  const ongoingProjects = [
    {
      title: 'CPP Pipeline Project - HARAD',
      location: 'HARAD',
      status: 'In Progress',
      description: 'Central Processing Plant pipeline installation and commissioning',
      category: 'Pipeline'
    },
    {
      title: 'Sprinkler System Installation',
      location: 'Multiple Sites',
      status: 'In Progress',
      description: 'Fire safety sprinkler system installation across various facilities',
      category: 'Safety Systems'
    }
  ]

  const ProjectCard = ({ project, isOngoing = false }) => (
    <div className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300 border border-gray-100">
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isOngoing ? 'bg-orange-500' : 'bg-green-500'}`}></div>
          <span className={`text-sm font-medium ${isOngoing ? 'text-orange-600' : 'text-green-600'}`}>
            {project.status}
          </span>
        </div>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {project.category}
        </span>
      </div>
      
      <h3 className="text-xl font-bold text-gray-900 mb-2">{project.title}</h3>
      <p className="text-gray-600 mb-4">{project.description}</p>
      
      <div className="flex items-center text-sm text-gray-500">
        <MapPin className="w-4 h-4 mr-1" />
        {project.location}
      </div>
    </div>
  )

  return (
    <section id="projects" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
            Our Projects
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Major Projects & Achievements
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Showcasing our expertise through successful project deliveries across various industries 
            and our commitment to excellence in every undertaking.
          </p>
        </div>

        {/* Project Stats */}
        <div className="grid md:grid-cols-4 gap-6 mb-16">
          <div className="text-center p-6 bg-gradient-to-br from-primary/5 to-primary/10 rounded-xl">
            <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-6 h-6 text-primary" />
            </div>
            <div className="text-3xl font-bold text-primary mb-2">500+</div>
            <div className="text-sm text-gray-600">Projects Completed</div>
          </div>
          
          <div className="text-center p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl">
            <div className="w-12 h-12 bg-green-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="w-6 h-6 text-green-600" />
            </div>
            <div className="text-3xl font-bold text-green-600 mb-2">15+</div>
            <div className="text-sm text-gray-600">Years Experience</div>
          </div>
          
          <div className="text-center p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl">
            <div className="w-12 h-12 bg-orange-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <Clock className="w-6 h-6 text-orange-600" />
            </div>
            <div className="text-3xl font-bold text-orange-600 mb-2">2</div>
            <div className="text-sm text-gray-600">Ongoing Projects</div>
          </div>
          
          <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl">
            <div className="w-12 h-12 bg-purple-200 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-6 h-6 text-purple-600" />
            </div>
            <div className="text-3xl font-bold text-purple-600 mb-2">3</div>
            <div className="text-sm text-gray-600">Office Locations</div>
          </div>
        </div>

        {/* Completed Projects */}
        <div className="mb-16">
          <div className="flex items-center mb-8">
            <CheckCircle className="w-6 h-6 text-green-600 mr-3" />
            <h3 className="text-2xl font-bold text-gray-900">Recently Completed Projects</h3>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6">
            {completedProjects.map((project, index) => (
              <ProjectCard key={index} project={project} />
            ))}
          </div>
        </div>

        {/* Ongoing Projects */}
        <div>
          <div className="flex items-center mb-8">
            <Clock className="w-6 h-6 text-orange-600 mr-3" />
            <h3 className="text-2xl font-bold text-gray-900">Current Projects</h3>
          </div>
          <div className="grid md:grid-cols-2 gap-6">
            {ongoingProjects.map((project, index) => (
              <ProjectCard key={index} project={project} isOngoing={true} />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center bg-gray-50 rounded-2xl p-12">
          <h3 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Start Your Next Project?
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join our list of satisfied clients and experience the Elite Structure difference. 
            Contact us today for a consultation.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-lg font-medium transition-colors">
              Start a Project
            </button>
            <button className="border border-primary text-primary hover:bg-primary hover:text-white px-8 py-3 rounded-lg font-medium transition-colors">
              View All Projects
            </button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Projects

