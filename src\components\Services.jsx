import { Wrench, Zap, Building, Cog, ArrowRight } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import AnimatedBackground from './AnimatedBackground'

const Services = () => {
  const services = [
    {
      icon: Building,
      title: 'Oil & Gas Construction',
      description: 'Pipeline installation and maintenance, GRP/RTR piping systems, process equipment installation, offshore and onshore facilities.',
      features: ['Pipeline Installation', 'GRP/RTR Piping', 'Gas Processing Plants', 'Compression Stations'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: Cog,
      title: 'Refineries & Petrochemicals',
      description: 'Mechanical construction, electrical & instrumentation works, shutdown/turnaround services, process unit construction.',
      features: ['Mechanical Construction', 'E&I Works', 'Shutdown Services', 'Tank Farm Construction'],
      color: 'from-green-500 to-green-600'
    },
    {
      icon: Zap,
      title: 'Power & Desalination',
      description: 'Civil construction works, steel structure erection, plant maintenance services, boiler and turbine installation.',
      features: ['Civil Construction', 'Steel Structure', 'Plant Maintenance', 'Water Treatment'],
      color: 'from-purple-500 to-purple-600'
    },
    {
      icon: Wrench,
      title: 'Specialized Services',
      description: 'Non-metallic piping (GRP, RTR, HDPE), scaffolding services, industrial maintenance, emergency repair services.',
      features: ['Non-metallic Piping', 'Scaffolding', 'Emergency Repairs', 'Quality Control'],
      color: 'from-orange-500 to-orange-600'
    }
  ]

  return (
    <section id="services" className="py-20 bg-gray-50 relative overflow-hidden">
      <AnimatedBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-primary/10 rounded-full text-sm font-medium text-primary mb-6">
            Our Services
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Comprehensive Engineering Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From oil & gas to power generation, we deliver specialized construction and engineering 
            services across multiple industries with unmatched expertise.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          {services.map((service, index) => (
            <div key={index} className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-xl transition-all duration-300 border border-gray-100">
              {/* Icon */}
              <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <service.icon className="w-8 h-8 text-white" />
              </div>

              {/* Content */}
              <h3 className="text-2xl font-bold text-gray-900 mb-4">{service.title}</h3>
              <p className="text-gray-600 mb-6">{service.description}</p>

              {/* Features */}
              <div className="grid grid-cols-2 gap-3 mb-6">
                {service.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-center text-sm text-gray-600">
                    <div className="w-2 h-2 bg-primary rounded-full mr-2"></div>
                    {feature}
                  </div>
                ))}
              </div>

              {/* CTA */}
              <Button variant="ghost" className="text-primary hover:text-primary hover:bg-primary/10 p-0">
                Learn More
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Button>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center bg-gradient-to-r from-primary to-primary/80 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">Ready to Start Your Project?</h3>
          <p className="text-xl mb-8 opacity-90">
            Get in touch with our expert team for a consultation and detailed project proposal.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary" className="bg-white text-primary hover:bg-gray-100">
              Request Quote
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-primary">
              Contact Us
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Services

