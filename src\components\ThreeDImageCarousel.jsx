import { useState, useEffect, useRef, useMemo } from 'react'
import { <PERSON>vas, use<PERSON>rame, useLoader } from '@react-three/fiber'
import { OrbitControls, Text, Float, Environment, ContactShadows, PerspectiveCamera, useTexture } from '@react-three/drei'
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react'
import * as THREE from 'three'

// 3D Image Plane Component
function ImagePlane({ texture, position, rotation, scale, opacity = 1 }) {
  const meshRef = useRef()
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.material.opacity = opacity
      meshRef.current.rotation.y = rotation + Math.sin(state.clock.elapsedTime * 0.5) * 0.02
    }
  })

  return (
    <mesh ref={meshRef} position={position} scale={scale}>
      <planeGeometry args={[2, 1.5]} />
      <meshStandardMaterial 
        map={texture} 
        transparent 
        opacity={opacity}
        side={THREE.DoubleSide}
      />
    </mesh>
  )
}

// 3D Carousel Scene Component
function CarouselScene({ currentIndex, images }) {
  const groupRef = useRef()
  const textures = useTexture(images.map(img => img.src))
  
  useFrame((state) => {
    if (groupRef.current) {
      // Smooth rotation animation
      const targetRotation = -currentIndex * (Math.PI * 2) / images.length
      groupRef.current.rotation.y = THREE.MathUtils.lerp(
        groupRef.current.rotation.y, 
        targetRotation, 
        0.05
      )
      
      // Floating animation
      groupRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.1
    }
  })

  const imagePositions = useMemo(() => {
    return images.map((_, index) => {
      const angle = (index / images.length) * Math.PI * 2
      const radius = 2.5
      return [
        Math.sin(angle) * radius,
        0,
        Math.cos(angle) * radius
      ]
    })
  }, [images.length])

  return (
    <group ref={groupRef}>
      {images.map((image, index) => {
        const isActive = index === currentIndex
        const opacity = isActive ? 1 : 0.6
        const scale = isActive ? [1.2, 1.2, 1.2] : [1, 1, 1]
        
        return (
          <Float
            key={index}
            speed={isActive ? 2 : 1}
            rotationIntensity={isActive ? 0.3 : 0.1}
            floatIntensity={isActive ? 0.5 : 0.2}
          >
            <ImagePlane
              texture={textures[index]}
              position={imagePositions[index]}
              rotation={0}
              scale={scale}
              opacity={opacity}
            />
          </Float>
        )
      })}
      
      {/* Center glow effect */}
      <pointLight position={[0, 0, 0]} intensity={0.5} color="#60a5fa" />
      
      {/* Floating particles */}
      {Array.from({ length: 30 }).map((_, i) => (
        <Float
          key={i}
          speed={1 + Math.random() * 2}
          rotationIntensity={0.5}
          floatIntensity={0.8}
        >
          <mesh position={[
            (Math.random() - 0.5) * 10,
            (Math.random() - 0.5) * 6,
            (Math.random() - 0.5) * 10
          ]}>
            <sphereGeometry args={[0.01 + Math.random() * 0.03]} />
            <meshStandardMaterial
              color={i % 3 === 0 ? "#60a5fa" : i % 3 === 1 ? "#f59e0b" : "#10b981"}
              emissive={i % 3 === 0 ? "#1e40af" : i % 3 === 1 ? "#d97706" : "#047857"}
              emissiveIntensity={0.4 + Math.random() * 0.3}
              transparent
              opacity={0.7}
            />
          </mesh>
        </Float>
      ))}

      {/* Additional light rays */}
      <spotLight
        position={[5, 5, 5]}
        intensity={0.3}
        angle={0.2}
        penumbra={1}
        color="#60a5fa"
        castShadow
      />
      <spotLight
        position={[-5, 5, -5]}
        intensity={0.3}
        angle={0.2}
        penumbra={1}
        color="#f59e0b"
        castShadow
      />
    </group>
  )
}

// Main 3D Image Carousel Component
const ThreeDImageCarousel = () => {
  const images = [
    {
      src: '/b1.jpg',
      alt: 'Construction Project 1',
      title: 'Industrial Construction',
      description: 'Advanced industrial facility construction'
    },
    {
      src: '/b2.jpg',
      alt: 'Construction Project 2', 
      title: 'Infrastructure Development',
      description: 'Modern infrastructure solutions'
    },
    {
      src: '/b3.jpg',
      alt: 'Construction Project 3',
      title: 'Engineering Excellence',
      description: 'Precision engineering projects'
    }
  ]

  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)
  const [isHovered, setIsHovered] = useState(false)

  // Auto-scroll functionality
  useEffect(() => {
    if (!isAutoPlaying || isHovered) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      )
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying, isHovered, images.length])

  const goToSlide = (index) => {
    setCurrentIndex(index)
  }

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? images.length - 1 : currentIndex - 1)
  }

  const goToNext = () => {
    setCurrentIndex(currentIndex === images.length - 1 ? 0 : currentIndex + 1)
  }

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying)
  }

  return (
    <div 
      className="relative w-full h-full rounded-2xl overflow-hidden group"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      style={{
        background: `
          linear-gradient(135deg, #667eea 0%, #764ba2 100%),
          radial-gradient(circle at 30% 70%, rgba(255,255,255,0.1) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(255,255,255,0.05) 0%, transparent 50%)
        `,
        boxShadow: `
          0 25px 50px -12px rgba(0, 0, 0, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.2),
          0 0 100px rgba(59, 130, 246, 0.3)
        `,
        transform: 'perspective(1200px) rotateX(3deg) rotateY(-2deg) translateZ(20px)',
        transformStyle: 'preserve-3d',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)'
      }}
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 2, 6], fov: 50 }}
        style={{ 
          background: 'transparent',
          filter: 'drop-shadow(0 20px 40px rgba(0,0,0,0.3))'
        }}
        gl={{ 
          antialias: true, 
          alpha: true,
          powerPreference: "high-performance"
        }}
      >
        {/* Enhanced Lighting */}
        <ambientLight intensity={0.3} />
        <directionalLight 
          position={[10, 10, 5]} 
          intensity={1} 
          castShadow
          shadow-mapSize-width={2048}
          shadow-mapSize-height={2048}
        />
        <pointLight position={[-10, -10, -10]} intensity={0.3} color="#60a5fa" />
        <spotLight 
          position={[0, 10, 0]} 
          intensity={0.5} 
          angle={0.3} 
          penumbra={1} 
          color="#ffffff"
        />
        
        {/* Environment for reflections */}
        <Environment preset="city" />
        
        {/* 3D Carousel */}
        <CarouselScene currentIndex={currentIndex} images={images} />
        
        {/* Contact Shadows */}
        <ContactShadows 
          position={[0, -2, 0]} 
          opacity={0.4} 
          scale={10} 
          blur={2} 
          far={4} 
        />
        
        {/* Camera Controls */}
        <OrbitControls 
          enablePan={false}
          enableZoom={false}
          enableRotate={true}
          autoRotate={!isHovered}
          autoRotateSpeed={0.5}
          minPolarAngle={Math.PI / 3}
          maxPolarAngle={Math.PI / 1.5}
        />
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Navigation Arrows */}
        <button
          onClick={goToPrevious}
          className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full p-3 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 pointer-events-auto border border-white/20"
          aria-label="Previous image"
        >
          <ChevronLeft className="w-6 h-6 text-white" />
        </button>

        <button
          onClick={goToNext}
          className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full p-3 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110 pointer-events-auto border border-white/20"
          aria-label="Next image"
        >
          <ChevronRight className="w-6 h-6 text-white" />
        </button>

        {/* Content Info */}
        <div className="absolute bottom-6 left-6 right-6 text-white">
          <div className="bg-black/20 backdrop-blur-md rounded-xl p-4 border border-white/10">
            <h3 className="text-xl font-bold mb-2 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-100">
              {images[currentIndex].title}
            </h3>
            <p className="text-sm opacity-90 transform translate-y-4 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-500 delay-200">
              {images[currentIndex].description}
            </p>
          </div>
        </div>

        {/* Dots Indicator */}
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-3 pointer-events-auto">
          {images.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 border border-white/30 ${
                index === currentIndex 
                  ? 'bg-white w-8 shadow-lg' 
                  : 'bg-white/30 hover:bg-white/60'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>

        {/* Auto-play Control */}
        <button
          onClick={toggleAutoPlay}
          className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 backdrop-blur-md rounded-full p-3 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-auto border border-white/20"
          aria-label={isAutoPlaying ? 'Pause slideshow' : 'Play slideshow'}
        >
          {isAutoPlaying ? (
            <Pause className="w-5 h-5 text-white" />
          ) : (
            <Play className="w-5 h-5 text-white" />
          )}
        </button>
      </div>
    </div>
  )
}

export default ThreeDImageCarousel
